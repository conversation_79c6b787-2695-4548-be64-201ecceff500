import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Same function as in email-service.ts
function getLogoBase64(): string | null {
  try {
    const logoPath = path.join(process.cwd(), 'public', 'logo', 'UpZera_logo_emails.png');
    const logoBuffer = fs.readFileSync(logoPath);
    const base64Logo = logoBuffer.toString('base64');
    return `data:image/png;base64,${base64Logo}`;
  } catch (error) {
    console.warn('Could not load email logo file for base64 encoding:', error);
    return null;
  }
}

export async function GET() {
  return NextResponse.json({ 
    message: 'Debug Logo API endpoint. Use POST to see HTML test page with logo display.' 
  });
}

export async function POST() {
  try {
    // Get the base64 logo
    const logoBase64 = getLogoBase64();
    const fallbackLogoSrc = 'https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png';
    
    // Create HTML test page
    const html = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>UpZera Logo Debug Test</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
          background: #f5f5f5;
        }
        .container {
          background: white;
          padding: 30px;
          border-radius: 10px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .logo-test {
          border: 1px solid #ddd;
          padding: 20px;
          margin: 20px 0;
          border-radius: 8px;
          background: #fafafa;
        }
        .logo-test h3 {
          margin-top: 0;
          color: #7e22ce;
        }
        .logo-test img {
          max-height: 70px;
          margin: 10px 0;
          display: block;
        }
        .status {
          padding: 10px;
          border-radius: 5px;
          margin: 10px 0;
        }
        .success {
          background: #d4edda;
          color: #155724;
          border: 1px solid #c3e6cb;
        }
        .error {
          background: #f8d7da;
          color: #721c24;
          border: 1px solid #f5c6cb;
        }
        .info {
          background: #d1ecf1;
          color: #0c5460;
          border: 1px solid #bee5eb;
        }
        pre {
          background: #f8f9fa;
          padding: 15px;
          border-radius: 5px;
          overflow-x: auto;
          font-size: 12px;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>🔍 UpZera Logo Debug Test</h1>
        <p>This page tests how the UpZera logo loads in different scenarios, similar to how it's used in email templates.</p>
        
        <div class="status ${logoBase64 ? 'success' : 'error'}">
          <strong>Base64 Logo Status:</strong> ${logoBase64 ? '✅ Successfully loaded' : '❌ Failed to load'}
        </div>
        
        <div class="logo-test">
          <h3>1. Base64 Embedded Logo (Email Template Style)</h3>
          ${logoBase64 ? `
            <div class="status success">✅ Base64 logo available</div>
            <img src="${logoBase64}" alt="UpZera Logo (Base64)" />
            <div class="info">
              <strong>Note:</strong> This is how the logo appears in email templates. 
              Base64 encoding ensures the logo displays even when external images are blocked.
            </div>
          ` : `
            <div class="status error">❌ Base64 logo not available</div>
            <div class="info">
              Logo file not found at: <code>public/logo/UpZera_logo_3-nobkgr.png</code>
            </div>
          `}
        </div>
        
        <div class="logo-test">
          <h3>2. Fallback External URL Logo</h3>
          <img src="${fallbackLogoSrc}" alt="UpZera Logo (External)" 
               onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" />
          <div class="status error" style="display:none;">❌ External logo failed to load</div>
          <div class="info">
            <strong>Fallback URL:</strong> <code>${fallbackLogoSrc}</code><br>
            This is used when base64 encoding fails.
          </div>
        </div>
        
        <div class="logo-test">
          <h3>3. Email Template Preview</h3>
          <div style="max-width:600px; margin:0 auto; font-family:'Arial', sans-serif; background:#ffffff; border:1px solid #e5e7eb; border-radius:12px; overflow:hidden; box-shadow:0 4px 6px rgba(0,0,0,0.05);">
            <!-- Header with Logo -->
            <div style="background: linear-gradient(135deg, #7e22ce, #9333ea); padding:28px; text-align:center; border-bottom:1px solid #a855f7;">
              <img src="${logoBase64 || fallbackLogoSrc}" alt="UpZera Logo" style="max-height:70px; filter: brightness(0) invert(1);">
            </div>
            <div style="padding:32px;">
              <h2 style="color:#7e22ce; font-size:24px; font-weight:bold; margin:0 0 24px 0; text-align:center;">
                Email Template Header Preview
              </h2>
              <p style="color:#4b5563; line-height:1.6; margin:0; font-size:16px;">
                This shows how the logo appears in actual email templates with the UpZera branding.
              </p>
            </div>
          </div>
        </div>
        
        <div class="logo-test">
          <h3>4. Technical Details</h3>
          <pre><code>Logo Path: ${path.join(process.cwd(), 'public', 'logo', 'UpZera_logo_3-nobkgr.png')}
Base64 Available: ${logoBase64 ? 'Yes' : 'No'}
Fallback URL: ${fallbackLogoSrc}
Base64 Length: ${logoBase64 ? logoBase64.length : 0} characters
Timestamp: ${new Date().toISOString()}</code></pre>
        </div>
        
        <div class="info">
          <strong>💡 How to use this endpoint:</strong><br>
          Send a POST request to <code>/api/debug-logo</code> to see this debug page.<br>
          This helps troubleshoot logo loading issues in email templates.
        </div>
      </div>
    </body>
    </html>
    `;

    return new NextResponse(html, {
      headers: {
        'Content-Type': 'text/html',
      },
    });
  } catch (error) {
    console.error('Error in debug-logo endpoint:', error);
    return NextResponse.json(
      {
        error: 'Failed to generate debug page',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
